<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MCT</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            --primary-color: #28909A;
            --secondary-color: #1c5c62;
            --accent-color-1: #F7A600; /* لون برتقالي ذهبي محدد */
            --accent-color-2: #008B80; /* لون أخضر فيروزي محدد */
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0f0f23, #1a1a2e, #16213e);
            color: #ffffff;
            overflow-x: hidden;
            position: relative;
        }

        /* خلفية متحركة */
        .animated-bg {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
            background: linear-gradient(135deg, #0f0f23, #1a1a2e, #16213e);
        }



        /* شبكة متحركة */
        

        @keyframes gridMove {
            0% { transform: translate(0, 0); }
            100% { transform: translate(50px, 50px); }
        }

        /* نقاط متحركة */
        .floating-particles {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
            pointer-events: none;
        }

        .particle {
            position: absolute;
            width: 8px;
            height: 8px;
            background:rgb(0, 151, 139);
            border-radius: 50%;
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); opacity: 0.5; }
            50% { transform: translateY(-20px) rotate(180deg); opacity: 1; }
        }

        /* Navbar */
        .navbar {
            position: fixed;
            top: 0;
            width: 100%;
            padding: 20px 5%;
            background:rgba(15, 15, 35, 0.95);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            z-index: 1000;
            transition: all 0.3s ease;
        }

        
.nav-container {
    display: flex;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
    position: relative;
    justify-content: space-between;
}


        
.logo-section {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-right: auto;
}


        .logo {
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, #008B80, #1c5c62);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            font-weight: bold;
            color: white;
            box-shadow: 0 8px 32px rgba(40, 144, 154, 0.3);
            animation: logoGlow 3s ease-in-out infinite alternate;
        }

        @keyframes logoGlow {
            0% { box-shadow: 0 8px 32px rgba(40, 144, 154, 0.3); }
            100% { box-shadow: 0 8px 32px rgba(40, 144, 154, 0.6); }
        }

        .company-name {
            font-size: 26px;
            font-weight: bold;
            background: linear-gradient(135deg, #ffffff, #ffffff);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        
.nav-links {
    display: flex;
    gap: 30px;
    list-style: none;
    margin: 0 auto;
}


        .nav-links a {
    position: relative;
    display: inline-block;
    overflow: hidden;

            color: #ffffff;
            text-decoration: none;
            font-weight: 500;
            padding: 10px 20px;
            border-radius: 25px;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        

        
.nav-links a:hover {
        color: #ffffff;
        transform: scale(1.05);
    }


        /* المحتوى الرئيسي */
        .main-content {
            margin-top: 100px;
            padding: 0 5%;
        }

        .section {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 50px 0;
            max-width: 1200px;
            margin: 0 auto;
        }

        /* الصفحة الرئيسية */
        .hero-section {
            text-align: center;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 40px;
        }

      .hero-logo {
            width: 200px;
            height: 200px;
            border-radius: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 80px;
            color: white;
            animation: heroFloat 3s ease-in-out infinite;
            position: relative;
            cursor: pointer;
        }

        .hero-logo img {
            transition: all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            position: relative;
            z-index: 10;
        }

        /* تأثير تناثر اللوغو */
        .logo-fragment {
            position: absolute;
            width: 20px;
            height: 20px;
            background: linear-gradient(135deg, #008B80, #F7A600);
            border-radius: 3px;
            opacity: 0;
            transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            pointer-events: none;
        }

        .hero-logo:hover .logo-fragment {
            opacity: 1;
        }

        .hero-logo:hover .logo-fragment:nth-child(1) { transform: translate(-60px, -60px) rotate(45deg); }
        .hero-logo:hover .logo-fragment:nth-child(2) { transform: translate(60px, -60px) rotate(-45deg); }
        .hero-logo:hover .logo-fragment:nth-child(3) { transform: translate(-60px, 60px) rotate(-45deg); }
        .hero-logo:hover .logo-fragment:nth-child(4) { transform: translate(60px, 60px) rotate(45deg); }
        .hero-logo:hover .logo-fragment:nth-child(5) { transform: translate(-80px, 0px) rotate(90deg); }
        .hero-logo:hover .logo-fragment:nth-child(6) { transform: translate(80px, 0px) rotate(-90deg); }
        .hero-logo:hover .logo-fragment:nth-child(7) { transform: translate(0px, -80px) rotate(180deg); }
        .hero-logo:hover .logo-fragment:nth-child(8) { transform: translate(0px, 80px) rotate(0deg); }

        .hero-logo:hover img {
            transform: scale(0.8) rotate(10deg);
            opacity: 0.7;
            filter: blur(1px);
        }

        @keyframes heroFloat {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }

        .hero-content h1 {
            font-size: 3.5rem;
            margin-bottom: 20px;
            background: linear-gradient(135deg, #ffffff, #ffffff);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            animation: textShine 3s ease-in-out infinite;
        }

        @keyframes textShine {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.8; }
        }

        .hero-content p {
            font-size: 1.3rem;
            line-height: 1.8;
            max-width: 800px;
            color: #b0b0b0;
            margin-bottom: 30px;
        }

        .cta-button {
            padding: 15px 40px;
            background: linear-gradient(135deg, #008B80, #1c5c62);
            color: white;
            border: none;
            border-radius: 30px;
            font-size: 1.1rem;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 10px 30px rgba(40, 144, 154, 0.3);
        }

        .cta-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 40px rgba(40, 144, 154, 0.5);
        }

        /* قسم الخدمات */
        .services-section {
            flex-direction: column;
            text-align: center;
        }

        .services-title {
            font-size: 2.8rem;
            margin-bottom: 60px;
            background: linear-gradient(135deg, #008B80, #F7A600);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            position: relative;
        }

        .services-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 100px;
            height: 4px;
            background: linear-gradient(135deg, #008B80, #F7A600);
            border-radius: 2px;
        }

        .services-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 30px;
            width: 100%;
            max-width: 1400px;
        }

        .service-card {
            background: rgba(255, 255, 255, 0.08);
            backdrop-filter: blur(25px);
            border: 2px solid rgba(255, 255, 255, 0.1);
            border-radius: 25px;
            padding: 45px 35px;
            text-align: center;
            transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            position: relative;
            overflow: hidden;
            cursor: pointer;
            transform-style: preserve-3d;
        }

        .service-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, rgba(0, 139, 128, 0.15), rgba(247, 166, 0, 0.15));
            transition: left 0.6s ease;
            z-index: -1;
        }

        .service-card::after {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(0, 139, 128, 0.1) 0%, transparent 70%);
            opacity: 0;
            transition: opacity 0.4s ease;
            z-index: -1;
        }

        .service-card:hover::before {
            left: 0;
        }

        .service-card:hover::after {
            opacity: 1;
        }

        .service-card:hover {
            transform: translateY(-15px) rotateX(5deg);
            border-color: rgba(0, 139, 128, 0.4);
            box-shadow: 0 25px 50px rgba(0, 139, 128, 0.3), 0 0 30px rgba(247, 166, 0, 0.2);
        }

        .service-icon {
            width: 100px;
            height: 100px;
            background: linear-gradient(135deg, #008B80, #F7A600);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 25px;
            font-size: 40px;
            color: white;
            position: relative;
            transition: all 0.4s ease;
            box-shadow: 0 10px 30px rgba(0, 139, 128, 0.3);
        }

        .service-icon::before {
            content: '';
            position: absolute;
            top: -5px;
            left: -5px;
            right: -5px;
            bottom: -5px;
            background: linear-gradient(135deg, #008B80, #F7A600);
            border-radius: 50%;
            z-index: -1;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .service-card:hover .service-icon {
            transform: scale(1.1) rotateY(360deg);
            box-shadow: 0 15px 40px rgba(0, 139, 128, 0.5);
        }

        .service-card:hover .service-icon::before {
            opacity: 0.3;
        }

        .service-card h3 {
            font-size: 1.6rem;
            margin-bottom: 20px;
            color: #008B80;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .service-card:hover h3 {
            color: #F7A600;
            transform: translateY(-2px);
        }

        .service-card p {
            color: #c0c0c0;
            line-height: 1.7;
            font-size: 1rem;
            transition: all 0.3s ease;
        }

        .service-card:hover p {
            color: #ffffff;
        }

        .service-card .learn-more {
            margin-top: 20px;
            padding: 12px 25px;
            background: linear-gradient(135deg, #008B80, #1c5c62);
            color: white;
            border: none;
            border-radius: 25px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            opacity: 0;
            transform: translateY(10px);
        }

        .service-card:hover .learn-more {
            opacity: 1;
            transform: translateY(0);
        }

        .service-card .learn-more:hover {
            background: linear-gradient(135deg, #F7A600, #008B80);
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(247, 166, 0, 0.4);
        }

        /* قسم التواصل */
        .contact-section {
            flex-direction: column;
            text-align: center;
            position: relative;
        }

        .contact-title {
            font-size: 3rem;
            margin-bottom: 60px;
            background: linear-gradient(135deg, #008B80, #F7A600);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            position: relative;
            font-weight: 700;
        }

        .contact-title::before {
            content: '';
            position: absolute;
            top: -20px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #008B80, #F7A600);
            border-radius: 50%;
            opacity: 0.1;
            z-index: -1;
        }

        .contact-title::after {
            content: '';
            position: absolute;
            bottom: -15px;
            left: 50%;
            transform: translateX(-50%);
            width: 120px;
            height: 4px;
            background: linear-gradient(135deg, #008B80, #F7A600);
            border-radius: 2px;
        }

        .contact-container {
            background: rgba(255, 255, 255, 0.08);
            backdrop-filter: blur(30px);
            border: 2px solid rgba(255, 255, 255, 0.15);
            border-radius: 35px;
            padding: 70px 50px;
            max-width: 1000px;
            width: 100%;
            box-shadow: 0 30px 80px rgba(0, 0, 0, 0.4), 0 0 50px rgba(0, 139, 128, 0.1);
            position: relative;
            overflow: hidden;
        }

        .contact-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(0, 139, 128, 0.05), rgba(247, 166, 0, 0.05));
            z-index: -1;
        }

        .contact-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 50px;
            margin-bottom: 50px;
        }

        .contact-item {
            text-align: center;
            padding: 30px 20px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 20px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            transition: all 0.4s ease;
            position: relative;
            overflow: hidden;
        }

        .contact-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, rgba(0, 139, 128, 0.1), rgba(247, 166, 0, 0.1));
            transition: left 0.5s ease;
            z-index: -1;
        }

        .contact-item:hover::before {
            left: 0;
        }

        .contact-item:hover {
            transform: translateY(-10px);
            border-color: rgba(0, 139, 128, 0.3);
            box-shadow: 0 20px 40px rgba(0, 139, 128, 0.2);
        }

        .contact-item h4 {
            font-size: 1.5rem;
            color: #008B80;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 15px;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .contact-item:hover h4 {
            color: #F7A600;
            transform: scale(1.05);
        }

        .contact-item p {
            color: #c0c0c0;
            line-height: 1.7;
            margin-bottom: 15px;
            font-size: 1rem;
            transition: all 0.3s ease;
        }

        .contact-item:hover p {
            color: #ffffff;
        }

        .contact-item a {
            color: #008B80;
            text-decoration: none;
            transition: all 0.3s ease;
            font-weight: 500;
            padding: 5px 10px;
            border-radius: 8px;
            display: inline-block;
        }

        .contact-item a:hover {
            color: #F7A600;
            background: rgba(247, 166, 0, 0.1);
            transform: translateY(-2px);
        }

        .social-section {
            margin-top: 40px;
            padding: 30px;
            background: rgba(255, 255, 255, 0.03);
            border-radius: 25px;
            border: 1px solid rgba(255, 255, 255, 0.08);
        }

        .social-section h4 {
            color: #008B80;
            margin-bottom: 25px;
            font-size: 1.4rem;
            font-weight: 600;
        }

        .social-links {
            display: flex;
            justify-content: center;
            gap: 25px;
            flex-wrap: wrap;
        }

        .social-link {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #008B80, #F7A600);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            text-decoration: none;
            font-size: 24px;
            transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            position: relative;
            overflow: hidden;
        }

        .social-link::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, #F7A600, #008B80);
            opacity: 0;
            transition: opacity 0.3s ease;
            border-radius: 50%;
        }

        .social-link:hover::before {
            opacity: 1;
        }

        .social-link:hover {
            transform: translateY(-8px) scale(1.15) rotate(360deg);
            box-shadow: 0 15px 35px rgba(0, 139, 128, 0.5), 0 0 25px rgba(247, 166, 0, 0.3);
        }

        .social-link span {
            position: relative;
            z-index: 2;
        }

        /* تأثيرات الظهور التدريجي */
        .fade-in-element {
            opacity: 0;
            transform: translateY(30px);
            transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }

        .fade-in-element.visible {
            opacity: 1;
            transform: translateY(0);
        }

        .fade-in-element.delay-1 {
            transition-delay: 0.2s;
        }

        .fade-in-element.delay-2 {
            transition-delay: 0.4s;
        }

        .fade-in-element.delay-3 {
            transition-delay: 0.6s;
        }

        .fade-in-element.delay-4 {
            transition-delay: 0.8s;
        }

        .fade-in-element.delay-5 {
            transition-delay: 1s;
        }

        .fade-in-element.delay-6 {
            transition-delay: 1.2s;
        }
        @media (max-width: 768px) {
            
.nav-links {
    display: flex;
    gap: 30px;
    list-style: none;
    margin: 0 auto;
}

            
            .hero-content h1 {
                font-size: 2.5rem;
            }
            
            .services-grid {
                grid-template-columns: 1fr;
            }
            
            .contact-grid {
                grid-template-columns: 1fr;
            }
        }
    
        .nav-links a::after {
        content: "";
        position: absolute;
        bottom: 8px;
        right: 20px;
        width: 0%;
        height: 2px;
        background: linear-gradient(90deg, #28909A, #1c5c62);
        transition: width 0.4s ease;
    }

        .nav-links a:hover::after {
        width: 60%;
    }


        .meteor {
            position: absolute;
            width: 3px;
            height: 3px;
            background: white;
            box-shadow: 0 0 16px 6px white;
            transform: rotate(35deg);
            animation: shoot 6s linear infinite;
            opacity: 0;
            z-index: 0;
        }

        @keyframes shoot {
            0% {
                top: +0%;
                left: +0%;
                opacity: 0;
            }
            10% {
                opacity: 1;
            }
            50% {
                top: 60%;
                left: 70%;
                opacity: 0.6;
            }
            100% {
                top: 110%;
                left: 110%;
                opacity: 0;
            }
        }

</style>
</head>
<body>
    <!-- خلفية متحركة -->
    <div class="animated-bg"></div>
    
    <div class="floating-particles" id="particles"></div>
    <div class="meteor"></div>

    <!-- شريط التنقل -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="logo-section">
                <div class="company-name"> MCT </div>
                <div class=""><img src="C:\Users\<USER>\Pictures\MCT-website\MCT1\image\logo-8.png" style="height: 50px; width: 50px;"></div>
                
            </div>
            <ul class="nav-links">
                <li><a href="#home">الرئيسية</a></li>
                <li><a href="#services">الخدمات</a></li>
                <li><a href="#contact">تواصل معنا</a></li>
                <li><a href="#about">عن الشركة</a></li>
                <li><a href="#projects">مشاريعنا</a></li>
                <li><a href="#blog">مدونة</a></li>
            
<li>
  <button id="lang-toggle" style="
    background: linear-gradient(135deg, #008B80, #1c5c62);
    color: white;
    border: none;
    margin-top: 5px;
    padding: 10px 15px;
    border-radius: 5px;
    font-weight: 500;
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.3s ease;
  ">EN</button>
</li>
            </ul>
        </div>
    </nav>

    <!-- المحتوى الرئيسي -->
    <main class="main-content">
        <!-- الصفحة الرئيسية -->
        <section id="home" class="section">
            <div class="hero-section">
                <div class="hero-logo fade-in-element">
                    <img src="C:\Users\<USER>\Pictures\MCT-website\MCT1\image\logo-8.png" style="height: 200px; width: 200px;">
                    <div class="logo-fragment"></div>
                    <div class="logo-fragment"></div>
                    <div class="logo-fragment"></div>
                    <div class="logo-fragment"></div>
                    <div class="logo-fragment"></div>
                    <div class="logo-fragment"></div>
                    <div class="logo-fragment"></div>
                    <div class="logo-fragment"></div>
                </div>
                <div class="hero-content">
                    <h1 class="fade-in-element delay-1"style= color: white >MCT</h1>
                    <p class="fade-in-element delay-2">
                        نحن شركة رائدة في مجال تكنولوجيا المعلومات والحلول التقنية المتطورة. نقدم خدمات شاملة في مجال الشبكات والبرمجيات وأمن المعلومات بأعلى معايير الجودة والاحترافية. فريقنا من الخبراء المتخصصين يعمل على تقديم حلول مبتكرة تلبي احتياجات عملائنا وتواكب التطورات التكنولوجية الحديثة.
                    </p>
                    <button class="cta-button fade-in-element delay-3">اكتشف خدماتنا</button>
                </div>
            </div>
        </section>

        <!-- قسم الخدمات -->
        <section id="services" class="section">
            <div class="services-section">
                <h2 class="services-title fade-in-element">خدماتنا المتميزة</h2>
                <div class="services-grid">
                    <div class="service-card fade-in-element delay-1" onclick="openServicePage('cybersecurity')">
                        <div class="service-icon">🔒</div>
                        <h3>أمن المعلومات</h3>
                        <p>حماية البيانات والأنظمة من التهديدات الإلكترونية وتطبيق أفضل ممارسات الأمان الرقمي المتقدمة.</p>
                        <button class="learn-more">اعرف المزيد</button>
                    </div>
                    <div class="service-card fade-in-element delay-2" onclick="openServicePage('networking')">
                        <div class="service-icon">🌐</div>
                        <h3>الشبكات</h3>
                        <p>تصميم وتنفيذ وصيانة الشبكات المحلية والواسعة بأحدث التقنيات وأعلى معايير الأمان والأداء.</p>
                        <button class="learn-more">اعرف المزيد</button>
                    </div>
                    <div class="service-card fade-in-element delay-3" onclick="openServicePage('software')">
                        <div class="service-icon">�</div>
                        <h3>البرمجيات</h3>
                        <p>تطوير التطبيقات والبرمجيات المخصصة باستخدام أحدث التقنيات لتلبية احتياجات العمل المختلفة.</p>
                        <button class="learn-more">اعرف المزيد</button>
                    </div>
                    <div class="service-card fade-in-element delay-4" onclick="openServicePage('security-assessment')">
                        <div class="service-icon">🔍</div>
                        <h3>الفحص الأمني</h3>
                        <p>تقييم وفحص الأنظمة والشبكات لاكتشاف الثغرات الأمنية وتقديم التوصيات اللازمة للحماية.</p>
                        <button class="learn-more">اعرف المزيد</button>
                    </div>
                    <div class="service-card fade-in-element delay-5" onclick="openServicePage('maintenance')">
                        <div class="service-icon">🔧</div>
                        <h3>الصيانة التقنية</h3>
                        <p>خدمات الصيانة الدورية والطارئة للأنظمة والمعدات التقنية لضمان استمرارية العمل بكفاءة عالية.</p>
                        <button class="learn-more">اعرف المزيد</button>
                    </div>
                    <div class="service-card fade-in-element delay-6" onclick="openServicePage('consulting')">
                        <div class="service-icon">⚡</div>
                        <h3>الاستشارات التقنية</h3>
                        <p>استشارات تقنية متخصصة، تدريب، دعم فني، وحلول مخصصة تلبي احتياجاتكم الخاصة في عالم التكنولوجيا.</p>
                        <button class="learn-more">اعرف المزيد</button>
                    </div>
                </div>
            </div>
        </section>

        <!-- قسم التواصل -->
        <section id="contact" class="section">
            <div class="contact-section">
                <h2 class="contact-title fade-in-element">تواصل معنا</h2>
                <div class="contact-container fade-in-element delay-1">
                    <div class="contact-grid">
                        <div class="contact-item">
                            <h4>📧 البريد الإلكتروني</h4>
                            <p><a href="mailto:<EMAIL>"><EMAIL></a></p>
                            <p><a href="mailto:<EMAIL>"><EMAIL></a></p>
                            <p><a href="mailto:<EMAIL>"><EMAIL></a></p>
                        </div>
                        <div class="contact-item">
                            <h4>📱 أرقام الهواتف</h4>
                            <p><a href="tel:+966123456789">+966 12 345 6789</a></p>
                            <p><a href="tel:+966987654321">+966 98 765 4321</a></p>
                            <p><a href="tel:+966555123456">+966 55 512 3456</a></p>
                        </div>
                    </div>

                    <div class="social-section">
                        <h4>🌐 تابعنا على وسائل التواصل الاجتماعي</h4>
                        <div class="social-links">
                            <a href="#" class="social-link" title="فيسبوك"><span>📘</span></a>
                            <a href="#" class="social-link" title="تويتر"><span>🐦</span></a>
                            <a href="#" class="social-link" title="لينكد إن"><span>💼</span></a>
                            <a href="#" class="social-link" title="انستغرام"><span>📷</span></a>
                            <a href="#" class="social-link" title="يوتيوب"><span>📺</span></a>
                            <a href="#" class="social-link" title="واتساب"><span>💬</span></a>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <script>
        // إنشاء الجسيمات المتحركة
        function createParticles() {
            const particlesContainer = document.getElementById('particles');
            const particleCount = 50;

            for (let i = 0; i < particleCount; i++) {
                const particle = document.createElement('div');
                particle.className = 'particle';
                particle.style.left = Math.random() * 100 + '%';
                particle.style.top = Math.random() * 100 + '%';
                particle.style.animationDelay = Math.random() * 6 + 's';
                particle.style.animationDuration = (Math.random() * 3 + 3) + 's';
                particlesContainer.appendChild(particle);
            }
        }

        // تأثير التمرير السلس
        function smoothScroll() {
            const navLinks = document.querySelectorAll('.nav-links a, .cta-button');
            navLinks.forEach(link => {
                link.addEventListener('click', (e) => {
                    if (link.getAttribute('href').startsWith('#')) {
                        e.preventDefault();
                        const targetId = link.getAttribute('href');
                        const targetSection = document.querySelector(targetId);
                        if (targetSection) {
                            targetSection.scrollIntoView({
                                behavior: 'smooth'
                            });
                        }
                    }
                });
            });
        }

        // تأثير شريط التنقل عند التمرير
        function navbarScrollEffect() {
            const navbar = document.querySelector('.navbar');
            window.addEventListener('scroll', () => {
                if (window.scrollY > 100) {
                    navbar.style.background = 'rgba(15, 15, 35, 0.98)';
                    navbar.style.boxShadow = '0 10px 30px rgba(0, 0, 0, 0.3)';
                } else {
                    navbar.style.background = 'rgba(15, 15, 35, 0.95)';
                    navbar.style.boxShadow = 'none';
                }
            });
        }

        // تأثير الظهور التدريجي عند التمرير
        function scrollAnimations() {
            const observerOptions = {
                threshold: 0.1,
                rootMargin: '0px 0px -50px 0px'
            };

            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.classList.add('visible');
                    }
                });
            }, observerOptions);

            // مراقبة جميع العناصر التي تحتوي على كلاس fade-in-element
            const fadeElements = document.querySelectorAll('.fade-in-element');
            fadeElements.forEach(element => {
                observer.observe(element);
            });
        }

        // تهيئة الموقع
        
        // تكرار حركة الشهاب
        const meteor = document.querySelector('.meteor');
        function randomizeMeteor() {
            if (meteor) {
                meteor.style.top = '-10%';
                meteor.style.left = '-10%';
                meteor.style.animationDelay = Math.random() * 5 + 's';
            }
        }
        setInterval(randomizeMeteor, 5000);

        function initWebsite() {
            createParticles();
            smoothScroll();
            navbarScrollEffect();
            scrollAnimations();
        }

        // تشغيل التهيئة عند تحميل الصفحة
        window.addEventListener('load', initWebsite);

        // فتح صفحات الخدمات
        function openServicePage(serviceType) {
            const servicePages = {
                'cybersecurity': 'cybersecurity.html',
                'networking': 'networking.html',
                'software': 'software.html',
                'security-assessment': 'security-assessment.html',
                'maintenance': 'maintenance.html',
                'consulting': 'consulting.html'
            };

            if (servicePages[serviceType]) {
                window.open(servicePages[serviceType], '_blank');
            }
        }
    </script>

<script>
document.getElementById('lang-toggle').addEventListener('click', function() {
    const btn = this;
    const isArabic = document.documentElement.lang === 'ar';
    
    // تغيير اللغة فقط (بدون تغيير اتجاه الصفحة للحفاظ على توضع العناصر)
    document.documentElement.lang = isArabic ? 'en' : 'ar';
    
    // تحديث نص الزر
    btn.textContent = isArabic ? 'AR' : 'EN';

    // قاموس الترجمات (عربي: إنجليزي)
    const translations = {
        // القائمة الرئيسية
        'الرئيسية': 'Home',
        'الخدمات': 'Services',
        'تواصل معنا': 'Contact Us',
        'عن الشركة': 'About Us',
        'مشاريعنا': 'Projects',
        'مدونة': 'Blog',
        
        // قسم البطل
        'MCT': 'MCT',
        'اكتشف خدماتنا': 'Explore Our Services',
        'نحن شركة رائدة في مجال تكنولوجيا المعلومات والحلول التقنية المتطورة. نقدم خدمات شاملة في مجال الشبكات والبرمجيات وأمن المعلومات بأعلى معايير الجودة والاحترافية. فريقنا من الخبراء المتخصصين يعمل على تقديم حلول مبتكرة تلبي احتياجات عملائنا وتواكب التطورات التكنولوجية الحديثة.':
        'We are a leading company in IT and advanced technical solutions, offering comprehensive services in networks, software, and cybersecurity. Our expert team delivers innovative solutions that meet our clients\' needs and align with modern tech trends.',
        
        // قسم الخدمات
        'خدماتنا المتميزة': 'Our Premium Services',
        'الشبكات': 'Networking',
        'تصميم وتنفيذ وصيانة الشبكات المحلية والواسعة بأحدث التقنيات وأعلى معايير الأمان والأداء.': 'Design, implementation, and maintenance of local and wide networks with the latest technologies and highest security standards.',
        'البرمجيات': 'Software',
        'تطوير التطبيقات والبرمجيات المخصصة باستخدام أحدث التقنيات لتلبية احتياجات العمل المختلفة.': 'Development of custom applications and software using the latest technologies to meet various business needs.',
        'أمن المعلومات': 'Cybersecurity',
        'حماية البيانات والأنظمة من التهديدات الإلكترونية وتطبيق أفضل ممارسات الأمان الرقمي.': 'Protection of data and systems from electronic threats and application of best digital security practices.',
        'الفحص الأمني': 'Security Assessment',
        'تقييم وفحص الأنظمة والشبكات لاكتشاف الثغرات الأمنية وتقديم التوصيات اللازمة.': 'Evaluation and testing of systems and networks to discover security vulnerabilities and provide necessary recommendations.',
        'الصيانة': 'Maintenance',
        'خدمات الصيانة الدورية والطارئة للأنظمة والمعدات التقنية لضمان استمرارية العمل.': 'Periodic and emergency maintenance services for systems and technical equipment to ensure business continuity.',
        'خدمات أخرى': 'Other Services',
        'استشارات تقنية، تدريب، دعم فني، وحلول مخصصة تلبي احتياجاتكم الخاصة في عالم التكنولوجيا.': 'Technical consultations, training, technical support, and customized solutions that meet your specific needs in the world of technology.',
        
        // قسم التواصل
        'تواصل معنا': 'Contact Us',
        '📧 البريد الإلكتروني': '📧 Email',
        '📱 أرقام الهواتف': '📱 Phone Numbers',
        '🌐 تابعنا على وسائل التواصل الاجتماعي': '🌐 Follow us on Social Media'
    };

    // تحديث جميع النصوص في الصفحة
    document.querySelectorAll('a, h1, h2, h3, h4, p, button').forEach(el => {
        const original = el.textContent.trim();
        
        // تجاهل بعض العناصر
        if (el.id === 'lang-toggle' || original === 'logo') {
            return;
        }
        
        if (isArabic) {
            // من العربي إلى الإنجليزي
            if (translations[original]) {
                el.textContent = translations[original];
            }
        } else {
            // من الإنجليزي إلى العربي
            for (const [ar, en] of Object.entries(translations)) {
                if (en === original) {
                    el.textContent = ar;
                    break;
                }
            }
        }
    });
});
</script>
</body>
</html>


